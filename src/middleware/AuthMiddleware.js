/**
 * Authentication Middleware for WebSocket Server
 * Handles JWT token validation and user authentication from Django
 */

const jwt = require('jsonwebtoken');
const Redis = require('ioredis');
const Logger = require('../utils/Logger');

class AuthMiddleware {
    constructor(config = {}) {
        this.jwtSecret = config.jwtSecret || process.env.JWT_SECRET || 'your-secret-key';
        this.redisUrl = config.redisUrl || process.env.REDIS_URL || 'redis://localhost:6379';
        this.testMode = config.testMode || process.env.AUTH_TEST_MODE === 'true';
        
        // Initialize Redis client for caching user sessions
        this.redis = new Redis(this.redisUrl, {
            retryDelayOnFailover: 100,
            maxRetriesPerRequest: 3,
            lazyConnect: true
        });
        
        // Initialize logger instance
        this.logger = new Logger();

        this.redis.on('connect', () => {
            this.logger.info('Redis connected for authentication cache');
        });

        this.redis.on('error', (err) => {
            this.logger.error('Redis connection error:', err);
        });
    }

    /**
     * Validate JWT token and extract user information
     * @param {string} token - JWT token from client
     * @returns {Object} - User information or null if invalid
     */
    async validateToken(token) {
        try {
            if (!token) {
                throw new Error('No token provided');
            }

            console.log("validate-token", token)

            // Remove 'Bearer ' prefix if present
            const cleanToken = token.replace(/^Bearer\s+/, '');

            // In test mode, bypass JWT verification and create a mock user
            if (this.testMode) {
                this.logger.info('Test mode enabled - bypassing JWT verification', {
                    service: 'realtime-yjs-server',
                    tokenLength: cleanToken.length
                });

                // Try to decode the token without verification to extract user info
                let userInfo = {};
                try {
                    const decoded = jwt.decode(cleanToken);
                    if (decoded) {
                        userInfo = {
                            userId: decoded.user_id || decoded.userId || decoded.sub || 1,
                            username: decoded.username || 'testuser',
                            email: decoded.email || '<EMAIL>',
                            permissions: decoded.permissions || ['read', 'write'],
                            exp: decoded.exp,
                            iat: decoded.iat
                        };
                    }
                } catch (decodeError) {
                    this.logger.info('Could not decode token, using default test user', {
                        service: 'realtime-yjs-server'
                    });
                }

                // Return default test user if decoding failed
                return {
                    userId: userInfo.userId || 1,
                    username: userInfo.username || 'testuser',
                    email: userInfo.email || '<EMAIL>',
                    permissions: userInfo.permissions || ['read', 'write'],
                    exp: userInfo.exp || Math.floor(Date.now() / 1000) + 86400, // 24 hours from now
                    iat: userInfo.iat || Math.floor(Date.now() / 1000)
                };
            }

            console.log('STEP 1: About to attempt JWT verification');
            console.log('STEP 2: Token length:', cleanToken.length);
            console.log('STEP 3: Secret available:', !!this.jwtSecret);

            this.logger.info(`Attempting JWT verification with secret: ${this.jwtSecret ? 'SECRET_SET' : 'SECRET_NOT_SET'}`, {
                service: 'realtime-yjs-server',
                tokenLength: cleanToken.length
            });

            console.log('STEP 4: About to call jwt.verify');
            console.log('STEP 5: Token:', cleanToken);
            console.log('STEP 6: Secret:', this.jwtSecret);

            const decoded = jwt.verify(cleanToken, this.jwtSecret);

            console.log('STEP 7: JWT verification successful!');

            this.logger.info('JWT verification successful', {
                service: 'realtime-yjs-server',
                userId: decoded.user_id || decoded.sub,
                username: decoded.username,
                exp: decoded.exp,
                currentTime: Math.floor(Date.now() / 1000)
            });

            // Check if token is expired
            if (decoded.exp && Date.now() >= decoded.exp * 1000) {
                throw new Error('Token expired');
            }

            return {
                userId: decoded.user_id || decoded.sub,
                username: decoded.username,
                email: decoded.email,
                permissions: decoded.permissions || [],
                exp: decoded.exp,
                iat: decoded.iat
            };
        } catch (error) {
            this.logger.warn('Token validation failed:', {
                service: 'realtime-yjs-server',
                error: error.message,
                errorName: error.name,
                tokenLength: token ? token.length : 0,
                secretSet: this.jwtSecret ? 'YES' : 'NO',
                testMode: this.testMode,
                tokenPreview: token ? token.substring(0, 50) + '...' : 'none'
            });
            return null;
        }
    }

    /**
     * Create user object from JWT token information
     * @param {Object} userInfo - User information from JWT
     * @returns {Object} - User data with permissions
     */
    async createUserFromJWT(userInfo) {
        try {
            this.logger.info('Creating user from JWT token', {
                userId: userInfo.user_id || userInfo.userId,
                username: userInfo.username,
                service: 'realtime-yjs-server'
            });

            // Create user object from JWT claims
            const user = {
                id: userInfo.user_id || userInfo.userId || userInfo.sub,
                username: userInfo.username || `user_${userInfo.user_id || userInfo.userId || userInfo.sub}`,
                email: userInfo.email || `${userInfo.username || 'user'}@example.com`,
                firstName: userInfo.first_name || userInfo.firstName || '',
                lastName: userInfo.last_name || userInfo.lastName || '',
                permissions: userInfo.permissions || ['read', 'write'],
                groups: userInfo.groups || [],
                isActive: true, // If JWT is valid, user is considered active
                isStaff: userInfo.is_staff || userInfo.isStaff || false,
                lastLogin: new Date().toISOString(),
                // Additional fields from JWT
                documentAccess: userInfo.document_access || [], // List of accessible document IDs
                roles: userInfo.roles || [],
                exp: userInfo.exp,
                iat: userInfo.iat
            };

            // Cache user data for token lifetime or 15 minutes, whichever is shorter
            const cacheKey = `user:${user.id}`;
            const cacheTime = userInfo.exp ?
                Math.min(userInfo.exp - Math.floor(Date.now() / 1000), 900) : 900;

            if (cacheTime > 0) {
                await this.redis.setex(cacheKey, cacheTime, JSON.stringify(user));
            }

            this.logger.info('User created from JWT successfully', {
                userId: user.id,
                username: user.username,
                permissions: user.permissions.length,
                service: 'realtime-yjs-server'
            });

            return user;

        } catch (error) {
            this.logger.error('Failed to create user from JWT:', {
                userId: userInfo.user_id || userInfo.userId,
                error: error.message,
                service: 'realtime-yjs-server'
            });
            return null;
        }
    }

    /**
     * Check if user has permission to access a document
     * @param {Object} user - User object
     * @param {string} documentId - Document ID
     * @returns {boolean} - Whether user can access document
     */
    checkDocumentAccess(user, documentId) {
        try {
            this.logger.info('Checking document access', {
                userId: user.id,
                documentId,
                service: 'realtime-yjs-server'
            });

            // Check if user has specific document access list in JWT
            if (user.documentAccess && Array.isArray(user.documentAccess)) {
                const hasAccess = user.documentAccess.includes(documentId) ||
                                user.documentAccess.includes('*'); // '*' means access to all documents

                this.logger.info('Document access check via JWT document list', {
                    userId: user.id,
                    documentId,
                    hasAccess,
                    documentAccessList: user.documentAccess,
                    service: 'realtime-yjs-server'
                });

                return hasAccess;
            }

            // Check permissions-based access
            if (user.permissions && Array.isArray(user.permissions)) {
                // Allow access if user has general document permissions
                const hasDocumentPermission = user.permissions.some(permission =>
                    permission.includes('document') ||
                    permission.includes('read') ||
                    permission.includes('write') ||
                    permission.includes('edit') ||
                    permission === '*'
                );

                if (hasDocumentPermission) {
                    this.logger.info('Document access granted via permissions', {
                        userId: user.id,
                        documentId,
                        permissions: user.permissions,
                        service: 'realtime-yjs-server'
                    });
                    return true;
                }
            }

            // Check role-based access
            if (user.roles && Array.isArray(user.roles)) {
                const hasDocumentRole = user.roles.some(role =>
                    role.includes('editor') ||
                    role.includes('collaborator') ||
                    role.includes('admin') ||
                    role === 'user'
                );

                if (hasDocumentRole) {
                    this.logger.info('Document access granted via roles', {
                        userId: user.id,
                        documentId,
                        roles: user.roles,
                        service: 'realtime-yjs-server'
                    });
                    return true;
                }
            }

            // Default: allow access for authenticated users (can be configured)
            const defaultAccess = process.env.DEFAULT_DOCUMENT_ACCESS !== 'false';

            this.logger.info('Document access check - using default policy', {
                userId: user.id,
                documentId,
                defaultAccess,
                service: 'realtime-yjs-server'
            });

            return defaultAccess;

        } catch (error) {
            this.logger.error('Document access check failed:', {
                userId: user.id,
                documentId,
                error: error.message,
                service: 'realtime-yjs-server'
            });
            // Default to deny access on error
            return false;
        }
    }

    /**
     * Authenticate WebSocket connection
     * @param {Object} socket - WebSocket connection
     * @param {Function} next - Next middleware function
     */
    async authenticateConnection(socket, next) {
        try {
            const token = socket.handshake.auth?.token ||
                         socket.handshake.headers?.authorization ||
                         socket.handshake.query?.token;

            if (!token) {
                return next(new Error('Authentication token required'));
            }

            // Validate JWT token
            const userInfo = await this.validateToken(token);
            if (!userInfo) {
                return next(new Error('Invalid or expired token'));
            }

            // Create user from JWT claims (no Django dependency)
            userInfo.token = token.replace(/^Bearer\s+/, '');
            const user = await this.createUserFromJWT(userInfo);
            if (!user || !user.isActive) {
                return next(new Error('User not found or inactive'));
            }

            // Attach user info to socket
            socket.user = user;
            socket.user.token = userInfo.token;

            this.logger.info('WebSocket connection authenticated', {
                userId: user.id,
                username: user.username,
                socketId: socket.id,
                service: 'realtime-yjs-server'
            });

            next();

        } catch (error) {
            this.logger.error('WebSocket authentication failed:', {
                error: error.message,
                service: 'realtime-yjs-server'
            });
            next(new Error('Authentication failed'));
        }
    }

    /**
     * Middleware for document access authorization
     * @param {Object} socket - WebSocket connection
     * @param {string} documentId - Document ID to access
     * @returns {boolean} - Whether access is granted
     */
    authorizeDocumentAccess(socket, documentId) {
        try {
            if (!socket.user) {
                this.logger.warn('Unauthorized document access attempt', {
                    documentId,
                    service: 'realtime-yjs-server'
                });
                return false;
            }

            const hasAccess = this.checkDocumentAccess(socket.user, documentId);

            if (hasAccess) {
                this.logger.info('Document access granted', {
                    userId: socket.user.id,
                    documentId,
                    socketId: socket.id,
                    service: 'realtime-yjs-server'
                });
            } else {
                this.logger.warn('Document access denied', {
                    userId: socket.user.id,
                    documentId,
                    socketId: socket.id,
                    service: 'realtime-yjs-server'
                });
            }

            return hasAccess;

        } catch (error) {
            this.logger.error('Document authorization error:', {
                error: error.message,
                service: 'realtime-yjs-server'
            });
            return false;
        }
    }

    /**
     * Clean up user session on disconnect
     * @param {Object} socket - WebSocket connection
     */
    async cleanupUserSession(socket) {
        if (socket.user) {
            this.logger.info('Cleaning up user session', {
                userId: socket.user.id,
                socketId: socket.id
            });
            
            // Could implement session cleanup logic here
            // For example, updating last seen timestamp
        }
    }

    /**
     * Get user session info
     * @param {string} userId - User ID
     * @returns {Object} - User session data
     */
    async getUserSession(userId) {
        try {
            const cacheKey = `user:${userId}`;
            const cachedUser = await this.redis.get(cacheKey);
            return cachedUser ? JSON.parse(cachedUser) : null;
        } catch (error) {
            this.logger.error('Failed to get user session:', error.message);
            return null;
        }
    }

    /**
     * Close Redis connection
     */
    async close() {
        if (this.redis) {
            await this.redis.quit();
        }
    }
}

module.exports = AuthMiddleware;
