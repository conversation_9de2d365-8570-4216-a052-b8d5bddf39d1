<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      http-equiv="Cache-Control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>Tiptap Collaborative Editor Test - v2.1</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
        padding: 30px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .header h1 {
        color: #2c3e50;
        margin: 0 0 15px 0;
        font-size: 2.5rem;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .header p {
        color: #6c757d;
        margin: 8px 0;
        font-size: 1.1rem;
        line-height: 1.6;
      }

      .container {
        max-width: 800px;
        margin: 0 auto 20px auto;
      }

      .client {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .client h3 {
        margin: 0 0 15px 0;
        color: #2c3e50;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .status {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .connected {
        background-color: #d4edda;
        color: #155724;
      }

      .disconnected {
        background-color: #f8d7da;
        color: #721c24;
      }

      .connecting {
        background-color: #fff3cd;
        color: #856404;
      }

      .controls {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        flex-wrap: wrap;
      }

      .controls input {
        flex: 1;
        min-width: 120px;
        padding: 8px 12px;
        border: 2px solid #e9ecef;
        border-radius: 6px;
        font-size: 14px;
      }

      .controls input:focus {
        outline: none;
        border-color: #007bff;
      }

      button {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.2s;
      }

      .btn-primary {
        background-color: #007bff;
        color: white;
      }

      .btn-primary:hover:not(:disabled) {
        background-color: #0056b3;
      }

      .btn-secondary {
        background-color: #6c757d;
        color: white;
      }

      .btn-secondary:hover:not(:disabled) {
        background-color: #545b62;
      }

      button:disabled {
        background-color: #e9ecef;
        color: #6c757d;
        cursor: not-allowed;
      }

      .editor-container {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 15px;
        background: white;
        overflow: hidden;
      }

      .editor-container.connected {
        border-color: #28a745;
      }

      .toolbar {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid #dee2e6;
        padding: 12px 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        align-items: center;
        border-radius: 12px 12px 0 0;
      }

      .toolbar-group {
        display: flex;
        gap: 2px;
        align-items: center;
        padding: 0 4px;
      }

      .toolbar-group:not(:last-child) {
        border-right: 1px solid #dee2e6;
        margin-right: 8px;
        padding-right: 8px;
      }

      .toolbar-button {
        background: transparent;
        border: 1px solid transparent;
        border-radius: 4px;
        padding: 6px 8px;
        cursor: pointer;
        font-size: 14px;
        color: #495057;
        transition: all 0.15s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 32px;
        height: 32px;
      }

      .toolbar-button:hover {
        background: #e9ecef;
        border-color: #ced4da;
      }

      .toolbar-button.is-active {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        border-color: #0056b3;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
      }

      .toolbar-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      /* Authentication Form Styles */
      .auth-form {
        max-width: 500px;
        margin: 0 auto;
        padding: 30px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #2c3e50;
        font-size: 14px;
      }

      .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
        font-size: 12px;
        resize: vertical;
        min-height: 100px;
        transition: border-color 0.3s ease;
      }

      .form-group textarea:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
      }

      .form-help {
        display: block;
        margin-top: 6px;
        font-size: 12px;
        color: #6c757d;
        font-style: italic;
      }

      /* User Info Display */
      .user-info {
        margin-bottom: 20px;
        padding: 15px;
        background: rgba(40, 167, 69, 0.1);
        border: 1px solid rgba(40, 167, 69, 0.2);
        border-radius: 8px;
      }

      .user-details {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        align-items: center;
      }

      .user-label {
        font-weight: 600;
        color: #2c3e50;
        font-size: 14px;
      }

      .user-value {
        color: #28a745;
        font-weight: 500;
        font-size: 14px;
      }

      .toolbar-select {
        background: white;
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 14px;
        color: #495057;
        cursor: pointer;
        min-width: 80px;
      }

      .toolbar-select:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }

      .color-picker {
        width: 32px;
        height: 32px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        cursor: pointer;
        background: white;
      }

      .ProseMirror {
        padding: 24px;
        min-height: 300px;
        outline: none;
        font-size: 16px;
        line-height: 1.7;
        background: white;
        border-radius: 0 0 12px 12px;
      }

      .ProseMirror p {
        margin: 0 0 1em 0;
      }

      .ProseMirror h1,
      .ProseMirror h2,
      .ProseMirror h3 {
        margin: 1.5em 0 0.5em 0;
        font-weight: 600;
      }

      .ProseMirror h1 {
        font-size: 1.8em;
      }
      .ProseMirror h2 {
        font-size: 1.5em;
      }
      .ProseMirror h3 {
        font-size: 1.3em;
      }

      .ProseMirror ul,
      .ProseMirror ol {
        padding-left: 1.5em;
      }

      .ProseMirror blockquote {
        border-left: 4px solid #e9ecef;
        padding-left: 1em;
        margin: 1em 0;
        font-style: italic;
      }

      .ProseMirror code {
        background-color: #f8f9fa;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: "Monaco", "Consolas", monospace;
      }

      .ProseMirror .editor-link {
        color: #007bff;
        text-decoration: underline;
        cursor: pointer;
      }

      .ProseMirror .editor-link:hover {
        color: #0056b3;
      }

      .ProseMirror mark {
        background-color: #fff3cd;
        padding: 0 2px;
        border-radius: 2px;
      }

      .ProseMirror [style*="text-align: center"] {
        text-align: center;
      }

      .ProseMirror [style*="text-align: right"] {
        text-align: right;
      }

      .ProseMirror [style*="text-align: justify"] {
        text-align: justify;
      }

      .ProseMirror sup {
        vertical-align: super;
        font-size: smaller;
      }

      .ProseMirror sub {
        vertical-align: sub;
        font-size: smaller;
      }

      .collaboration-cursor__caret {
        border-left: 1px solid #0d0d0d;
        border-right: 1px solid #0d0d0d;
        margin-left: -1px;
        margin-right: -1px;
        pointer-events: none;
        position: relative;
        word-break: normal;
      }

      .collaboration-cursor__label {
        border-radius: 3px 3px 3px 0;
        color: #0d0d0d;
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        left: -1px;
        line-height: normal;
        padding: 0.1rem 0.3rem;
        position: absolute;
        top: -1.4em;
        user-select: none;
        white-space: nowrap;
      }

      .log {
        height: 120px;
        overflow-y: auto;
        border: 1px solid #e9ecef;
        padding: 12px;
        font-family: "Monaco", "Consolas", monospace;
        font-size: 12px;
        background-color: #f8f9fa;
        border-radius: 6px;
        line-height: 1.4;
      }

      .log-entry {
        margin-bottom: 4px;
        padding: 2px 0;
      }

      .log-entry.info {
        color: #007bff;
      }
      .log-entry.success {
        color: #28a745;
      }
      .log-entry.warning {
        color: #ffc107;
      }
      .log-entry.error {
        color: #dc3545;
      }

      .stats {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-top: 30px;
      }

      .stats h3 {
        margin: 0 0 15px 0;
        color: #2c3e50;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
      }

      .stat-item {
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        text-align: center;
      }

      .stat-value {
        font-size: 28px;
        font-weight: 700;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .stat-label {
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      @media (max-width: 768px) {
        .controls {
          flex-direction: column;
        }

        .controls input {
          min-width: auto;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>🚀 Tiptap Collaborative Editor</h1>
      <p>
        <strong
          >Real-time collaborative rich text editing powered by YJS +
          Tiptap</strong
        >
      </p>
      <p>
        Open this page in multiple tabs or browsers to see real-time
        collaboration in action!
      </p>
    </div>

    <!-- JWT Authentication Section -->
    <div id="auth-section" class="container">
      <div class="client">
        <h3 style="color: #2c3e50; margin-bottom: 20px; text-align: center">
          🔐 Collaborative Editor Authentication
        </h3>
        <div class="auth-form">
          <div class="form-group">
            <label for="jwtToken">JWT Authentication Token:</label>
            <textarea
              id="jwtToken"
              placeholder="Paste your JWT token here..."
              rows="4"
            ></textarea>
            <small class="form-help"
              >Enter a valid JWT token to access the collaborative editor</small
            >
          </div>
          <button id="authenticate" class="btn-primary">
            Authenticate & Enter Editor
          </button>
        </div>
      </div>
    </div>

    <!-- Editor Section (Initially Hidden) -->
    <div id="editor-section" class="container" style="display: none">
      <div class="client">
        <h3>
          🚀 Collaborative Editor
          <div id="status" class="status disconnected">Disconnected</div>
        </h3>

        <div class="user-info">
          <div class="user-details">
            <span class="user-label">👤 User:</span>
            <span id="displayUsername" class="user-value">Loading...</span>
            <span class="user-label">📧 Email:</span>
            <span id="displayEmail" class="user-value">Loading...</span>
          </div>
        </div>

        <div class="controls">
          <input
            id="documentId"
            type="text"
            placeholder="Document ID"
            value="tiptap-demo"
          />
          <input
            id="userId"
            type="text"
            placeholder="Your Name"
            value=""
            readonly
          />
          <button id="connect" class="btn-primary">Connect to Document</button>
          <button id="disconnect" class="btn-secondary" disabled>
            Disconnect
          </button>
          <button id="logout" class="btn-secondary">Logout</button>
        </div>

        <div id="editor-container" class="editor-container">
          <div id="toolbar" class="toolbar">
            <!-- Text Formatting Group -->
            <div class="toolbar-group">
              <button
                id="bold-btn"
                class="toolbar-button"
                title="Bold (Ctrl+B)"
              >
                <strong>B</strong>
              </button>
              <button
                id="italic-btn"
                class="toolbar-button"
                title="Italic (Ctrl+I)"
              >
                <em>I</em>
              </button>
              <button
                id="underline-btn"
                class="toolbar-button"
                title="Underline (Ctrl+U)"
              >
                <u>U</u>
              </button>
              <button
                id="strike-btn"
                class="toolbar-button"
                title="Strikethrough"
              >
                <s>S</s>
              </button>
            </div>

            <!-- Headings Group -->
            <div class="toolbar-group">
              <select
                id="heading-select"
                class="toolbar-select"
                title="Heading Level"
              >
                <option value="paragraph">Paragraph</option>
                <option value="1">Heading 1</option>
                <option value="2">Heading 2</option>
                <option value="3">Heading 3</option>
                <option value="4">Heading 4</option>
                <option value="5">Heading 5</option>
                <option value="6">Heading 6</option>
              </select>
            </div>

            <!-- Lists Group -->
            <div class="toolbar-group">
              <button
                id="bullet-list-btn"
                class="toolbar-button"
                title="Bullet List"
              >
                • List
              </button>
              <button
                id="ordered-list-btn"
                class="toolbar-button"
                title="Numbered List"
              >
                1. List
              </button>
            </div>

            <!-- Text Alignment Group -->
            <div class="toolbar-group">
              <button
                id="align-left-btn"
                class="toolbar-button"
                title="Align Left"
              >
                ⬅
              </button>
              <button
                id="align-center-btn"
                class="toolbar-button"
                title="Align Center"
              >
                ↔
              </button>
              <button
                id="align-right-btn"
                class="toolbar-button"
                title="Align Right"
              >
                ➡
              </button>
              <button
                id="align-justify-btn"
                class="toolbar-button"
                title="Justify"
              >
                ⬌
              </button>
            </div>

            <!-- Colors Group -->
            <div class="toolbar-group">
              <input
                id="text-color"
                type="color"
                class="color-picker"
                title="Text Color"
                value="#000000"
              />
              <input
                id="highlight-color"
                type="color"
                class="color-picker"
                title="Highlight Color"
                value="#ffff00"
              />
            </div>

            <!-- Special Formatting Group -->
            <div class="toolbar-group">
              <button id="code-btn" class="toolbar-button" title="Inline Code">
                &lt;/&gt;
              </button>
              <button
                id="blockquote-btn"
                class="toolbar-button"
                title="Blockquote"
              >
                "
              </button>
              <button
                id="horizontal-rule-btn"
                class="toolbar-button"
                title="Horizontal Rule"
              >
                ―
              </button>
            </div>

            <!-- Link Group -->
            <div class="toolbar-group">
              <button id="link-btn" class="toolbar-button" title="Insert Link">
                🔗
              </button>
            </div>

            <!-- Superscript/Subscript Group -->
            <div class="toolbar-group">
              <button
                id="superscript-btn"
                class="toolbar-button"
                title="Superscript"
              >
                x²
              </button>
              <button
                id="subscript-btn"
                class="toolbar-button"
                title="Subscript"
              >
                x₂
              </button>
            </div>
          </div>
          <div id="editor"></div>
        </div>

        <div class="log" id="log"></div>
      </div>
    </div>

    <div class="stats">
      <h3>📊 Server Statistics</h3>
      <div id="stats" class="stats-grid">
        <div class="stat-item">
          <div class="stat-value" id="stat-connections">-</div>
          <div class="stat-label">Active Connections</div>
        </div>
        <div class="stat-item">
          <div class="stat-value" id="stat-documents">-</div>
          <div class="stat-label">Active Documents</div>
        </div>
        <div class="stat-item">
          <div class="stat-value" id="stat-uptime">-</div>
          <div class="stat-label">Server Uptime</div>
        </div>
      </div>
    </div>

    <!-- Load Tiptap Bundle -->
    <script src="./tiptap-bundle.js"></script>

    <script>
      // Check if all required libraries are loaded
      if (typeof Y === "undefined") {
        document.body.innerHTML =
          "<h1>❌ Error: Y.js library failed to load</h1><p>Bundle not found or failed to load.</p>";
        throw new Error("Y.js library not loaded");
      }

      if (typeof WebsocketProvider === "undefined") {
        document.body.innerHTML =
          "<h1>❌ Error: WebsocketProvider failed to load</h1><p>Bundle not found or failed to load.</p>";
        throw new Error("WebsocketProvider not loaded");
      }

      if (typeof TiptapEditor === "undefined") {
        document.body.innerHTML =
          "<h1>❌ Error: Tiptap Editor failed to load</h1><p>Bundle not found or failed to load.</p>";
        throw new Error("Tiptap Editor not loaded");
      }

      console.log("✅ All libraries loaded successfully");
      console.log("Y.js:", Y);
      console.log("WebsocketProvider:", WebsocketProvider);
      console.log("TiptapEditor:", TiptapEditor);

      // Client class for managing Tiptap collaborative editing
      class TiptapCollaborativeClient {
        constructor() {
          this.doc = null;
          this.provider = null;
          this.editor = null;
          this.connected = false;
          this.authenticated = false;
          this.currentUser = null;
          this.jwtToken = null;

          // DOM elements - Auth section
          this.authSection = document.getElementById("auth-section");
          this.editorSection = document.getElementById("editor-section");
          this.jwtTokenEl = document.getElementById("jwtToken");
          this.authenticateBtn = document.getElementById("authenticate");

          // DOM elements - Editor section
          this.documentIdEl = document.getElementById("documentId");
          this.userIdEl = document.getElementById("userId");
          this.statusEl = document.getElementById("status");
          this.connectBtn = document.getElementById("connect");
          this.disconnectBtn = document.getElementById("disconnect");
          this.logoutBtn = document.getElementById("logout");
          this.editorContainer = document.getElementById("editor-container");
          this.editorEl = document.getElementById("editor");
          this.logEl = document.getElementById("log");
          this.displayUsernameEl = document.getElementById("displayUsername");
          this.displayEmailEl = document.getElementById("displayEmail");

          // Bind events
          this.authenticateBtn.addEventListener("click", () =>
            this.authenticate()
          );
          this.connectBtn.addEventListener("click", () => this.connect());
          this.disconnectBtn.addEventListener("click", () => this.disconnect());
          this.logoutBtn.addEventListener("click", () => this.logout());

          // Check for existing authentication
          this.checkExistingAuth();

          this.log("🚀 Tiptap client initialized", "info");
        }

        log(message, type = "info") {
          const timestamp = new Date().toLocaleTimeString();
          const logEntry = document.createElement("div");
          logEntry.className = `log-entry ${type}`;
          logEntry.textContent = `[${timestamp}] ${message}`;
          this.logEl.appendChild(logEntry);
          this.logEl.scrollTop = this.logEl.scrollHeight;
          console.log(`[Client] ${message}`);
        }

        checkExistingAuth() {
          // Check if there's a stored JWT token
          const storedToken = localStorage.getItem("jwtToken");
          if (storedToken) {
            this.jwtTokenEl.value = storedToken;
            // Auto-authenticate if token exists
            setTimeout(() => {
              this.authenticate();
            }, 500);
          }
        }

        authenticate() {
          const token = this.jwtTokenEl.value.trim();

          if (!token) {
            alert("Please enter a JWT token");
            return;
          }

          try {
            // Decode JWT token to extract user information
            const userInfo = this.decodeJWT(token);

            if (!userInfo) {
              alert("Invalid JWT token format");
              return;
            }

            // Store authentication data
            this.jwtToken = token;
            this.currentUser = userInfo;
            this.authenticated = true;

            // Store token for future sessions
            localStorage.setItem("jwtToken", token);

            // Show success and transition to editor
            this.log("✅ Authentication successful", "success");
            this.showEditorSection();
          } catch (error) {
            console.error("Authentication error:", error);
            alert("Failed to authenticate. Please check your JWT token.");
          }
        }

        decodeJWT(token) {
          try {
            // Split the JWT token
            const parts = token.split(".");
            if (parts.length !== 3) {
              throw new Error("Invalid JWT format");
            }

            // Decode the payload (second part)
            const payload = parts[1];
            const decoded = atob(payload.replace(/-/g, "+").replace(/_/g, "/"));
            const userInfo = JSON.parse(decoded);

            // Validate required fields
            if (!userInfo.user_id && !userInfo.sub) {
              throw new Error("JWT missing user ID");
            }

            // Normalize the user info
            return {
              id: userInfo.user_id || userInfo.sub,
              username:
                userInfo.username ||
                userInfo.name ||
                `User${userInfo.user_id || userInfo.sub}`,
              email: userInfo.email || "No email provided",
              permissions: userInfo.permissions || [],
              roles: userInfo.roles || [],
              document_access: userInfo.document_access || [],
              exp: userInfo.exp,
            };
          } catch (error) {
            console.error("JWT decode error:", error);
            return null;
          }
        }

        showEditorSection() {
          // Hide auth section, show editor section
          this.authSection.style.display = "none";
          this.editorSection.style.display = "block";

          // Populate user information
          this.displayUsernameEl.textContent = this.currentUser.username;
          this.displayEmailEl.textContent = this.currentUser.email;
          this.userIdEl.value = this.currentUser.username;

          // Auto-connect to document
          setTimeout(() => {
            this.connect();
          }, 1000);
        }

        logout() {
          // Clear authentication
          this.authenticated = false;
          this.currentUser = null;
          this.jwtToken = null;
          localStorage.removeItem("jwtToken");

          // Disconnect if connected
          if (this.connected) {
            this.disconnect();
          }

          // Show auth section, hide editor section
          this.authSection.style.display = "block";
          this.editorSection.style.display = "none";

          // Clear form
          this.jwtTokenEl.value = "";

          this.log("👋 Logged out successfully", "info");
        }

        connect() {
          // Check if user is authenticated
          if (!this.authenticated || !this.jwtToken) {
            alert("Please authenticate first");
            return;
          }

          const documentId = this.documentIdEl.value.trim();
          const userId = this.userIdEl.value.trim();

          if (!documentId || !userId) {
            alert("Please enter both Document ID and User Name");
            return;
          }

          this.log(
            "🔌 Connecting to YJS server with JWT authentication...",
            "info"
          );
          this.updateStatus("connecting");

          try {
            // Create YJS document
            this.doc = new Y.Doc();

            // Create WebSocket provider with JWT authentication
            // Best practice: Use 'params' option if y-websocket >= 1.4.0
            this.provider = new WebsocketProvider(
              'ws://localhost:3000',      // Just the origin, no document ID or query
              documentId,                 // Document ID, gets added as path
              this.doc,
              { params: { token: this.jwtToken } } // y-websocket auto-appends as query param
            );

            // Setup provider listeners
            this.setupProviderListeners();

            // Create Tiptap editor
            this.createEditor(userId);
          } catch (error) {
            this.log(`❌ Connection failed: ${error.message}`, "error");
            this.updateStatus("disconnected");
          }
        }

        setupProviderListeners() {
          this.provider.on("status", (event) => {
            this.log(`📡 Provider status: ${event.status}`, "info");

            if (event.status === "connected") {
              this.connected = true;
              this.updateStatus("connected");
              this.editorContainer.classList.add("connected");
              this.connectBtn.disabled = true;
              this.disconnectBtn.disabled = false;
              this.log("✅ Successfully connected to server!", "success");
            } else if (event.status === "disconnected") {
              this.connected = false;
              this.updateStatus("disconnected");
              this.editorContainer.classList.remove("connected");
              this.connectBtn.disabled = false;
              this.disconnectBtn.disabled = true;
              this.log("🔌 Disconnected from server", "warning");
            }
          });

          this.provider.on("connection-error", (error) => {
            this.log(`❌ Connection error: ${error}`, "error");
          });

          this.provider.on("connection-close", (event) => {
            this.log(
              `🔌 Connection closed: ${event.code} ${event.reason}`,
              "warning"
            );
          });
        }

        createEditor(userId) {
          // Generate a random color for this user
          const colors = [
            "#ff6b6b",
            "#4ecdc4",
            "#45b7d1",
            "#96ceb4",
            "#feca57",
            "#ff9ff3",
            "#54a0ff",
          ];
          const userColor = colors[Math.floor(Math.random() * colors.length)];

          this.editor = new TiptapEditor({
            element: this.editorEl,
            extensions: [
              TiptapStarterKit.configure({
                history: false, // Important: disable history for collaboration
              }),
              TiptapCollaboration.configure({
                document: this.doc,
              }),
              TiptapCollaborationCursor.configure({
                provider: this.provider,
                user: {
                  name: userId,
                  color: userColor,
                },
              }),
              // Rich text extensions
              TiptapTextStyle,
              TiptapUnderline,
              TiptapTextAlign.configure({
                types: ["heading", "paragraph"],
              }),
              TiptapLink.configure({
                openOnClick: false,
                HTMLAttributes: {
                  class: "editor-link",
                },
              }),
              TiptapColor.configure({
                types: ["textStyle"],
              }),
              TiptapHighlight.configure({
                multicolor: true,
              }),
              TiptapSuperscript,
              TiptapSubscript,
            ],
            content: "<p>Start typing to collaborate in real-time! 🚀</p>",
            onUpdate: () => {
              this.log("📝 Document updated", "info");
              this.updateToolbarState();
            },
            onCreate: () => {
              this.log("✅ Tiptap editor created successfully", "success");
              this.setupToolbar();
              this.updateToolbarState();
            },
            onDestroy: () => {
              this.log("🗑️ Tiptap editor destroyed", "info");
            },
            onSelectionUpdate: () => {
              this.updateToolbarState();
            },
          });
        }

        setupToolbar() {
          // Text formatting buttons
          document.getElementById("bold-btn").addEventListener("click", () => {
            this.editor.chain().focus().toggleBold().run();
          });

          document
            .getElementById("italic-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().toggleItalic().run();
            });

          document
            .getElementById("underline-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().toggleUnderline().run();
            });

          document
            .getElementById("strike-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().toggleStrike().run();
            });

          // Heading selector
          document
            .getElementById("heading-select")
            .addEventListener("change", (e) => {
              const level = e.target.value;
              if (level === "paragraph") {
                this.editor.chain().focus().setParagraph().run();
              } else {
                this.editor
                  .chain()
                  .focus()
                  .toggleHeading({ level: parseInt(level) })
                  .run();
              }
            });

          // List buttons
          document
            .getElementById("bullet-list-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().toggleBulletList().run();
            });

          document
            .getElementById("ordered-list-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().toggleOrderedList().run();
            });

          // Text alignment buttons
          document
            .getElementById("align-left-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().setTextAlign("left").run();
            });

          document
            .getElementById("align-center-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().setTextAlign("center").run();
            });

          document
            .getElementById("align-right-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().setTextAlign("right").run();
            });

          document
            .getElementById("align-justify-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().setTextAlign("justify").run();
            });

          // Color pickers
          document
            .getElementById("text-color")
            .addEventListener("change", (e) => {
              this.editor.chain().focus().setColor(e.target.value).run();
            });

          document
            .getElementById("highlight-color")
            .addEventListener("change", (e) => {
              this.editor
                .chain()
                .focus()
                .toggleHighlight({ color: e.target.value })
                .run();
            });

          // Special formatting buttons
          document.getElementById("code-btn").addEventListener("click", () => {
            this.editor.chain().focus().toggleCode().run();
          });

          document
            .getElementById("blockquote-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().toggleBlockquote().run();
            });

          document
            .getElementById("horizontal-rule-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().setHorizontalRule().run();
            });

          // Link button
          document.getElementById("link-btn").addEventListener("click", () => {
            const url = window.prompt("Enter URL:");
            if (url) {
              this.editor.chain().focus().setLink({ href: url }).run();
            }
          });

          // Superscript and subscript buttons
          document
            .getElementById("superscript-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().toggleSuperscript().run();
            });

          document
            .getElementById("subscript-btn")
            .addEventListener("click", () => {
              this.editor.chain().focus().toggleSubscript().run();
            });
        }

        updateToolbarState() {
          if (!this.editor) return;

          // Update button active states
          const buttons = {
            "bold-btn": this.editor.isActive("bold"),
            "italic-btn": this.editor.isActive("italic"),
            "underline-btn": this.editor.isActive("underline"),
            "strike-btn": this.editor.isActive("strike"),
            "bullet-list-btn": this.editor.isActive("bulletList"),
            "ordered-list-btn": this.editor.isActive("orderedList"),
            "code-btn": this.editor.isActive("code"),
            "blockquote-btn": this.editor.isActive("blockquote"),
            "superscript-btn": this.editor.isActive("superscript"),
            "subscript-btn": this.editor.isActive("subscript"),
            "align-left-btn": this.editor.isActive({ textAlign: "left" }),
            "align-center-btn": this.editor.isActive({ textAlign: "center" }),
            "align-right-btn": this.editor.isActive({ textAlign: "right" }),
            "align-justify-btn": this.editor.isActive({ textAlign: "justify" }),
          };

          Object.entries(buttons).forEach(([id, isActive]) => {
            const button = document.getElementById(id);
            if (button) {
              button.classList.toggle("is-active", isActive);
            }
          });

          // Update heading selector
          const headingSelect = document.getElementById("heading-select");
          if (headingSelect) {
            let selectedValue = "paragraph";
            for (let level = 1; level <= 6; level++) {
              if (this.editor.isActive("heading", { level })) {
                selectedValue = level.toString();
                break;
              }
            }
            headingSelect.value = selectedValue;
          }
        }

        disconnect() {
          if (this.provider) {
            this.provider.destroy();
            this.provider = null;
          }

          if (this.editor) {
            this.editor.destroy();
            this.editor = null;
          }

          this.doc = null;
          this.connected = false;
          this.updateStatus("disconnected");
          this.editorContainer.classList.remove("connected");
          this.connectBtn.disabled = false;
          this.disconnectBtn.disabled = true;

          // Clear editor content
          this.editorEl.innerHTML = "";

          this.log("🔌 Disconnected and cleaned up", "info");
        }

        updateStatus(status) {
          this.statusEl.textContent =
            status.charAt(0).toUpperCase() + status.slice(1);
          this.statusEl.className = `status ${status}`;
        }
      }

      // Initialize single client
      const client = new TiptapCollaborativeClient();

      // Auto-fill JWT token from URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get("token");

      if (token) {
        // Pre-fill the JWT token field
        document.getElementById("jwtToken").value = token;
        // Auto-authenticate after a short delay
        setTimeout(() => {
          client.authenticate();
        }, 1000);
      }

      // Server statistics
      async function updateStats() {
        try {
          const response = await fetch("/api/stats");
          if (response.ok) {
            const stats = await response.json();

            document.getElementById("stat-connections").textContent =
              stats.connections?.totalConnections || 0;
            document.getElementById("stat-documents").textContent = Object.keys(
              stats.connections?.documentStats || {}
            ).length;

            // Format uptime (approximate from timestamp)
            const now = new Date();
            const statsTime = new Date(stats.timestamp);
            const uptimeMs = now - statsTime + 5 * 60 * 1000; // Rough estimate
            const uptimeSeconds = Math.floor(uptimeMs / 1000);
            const hours = Math.floor(uptimeSeconds / 3600);
            const minutes = Math.floor((uptimeSeconds % 3600) / 60);
            document.getElementById(
              "stat-uptime"
            ).textContent = `${hours}h ${minutes}m`;
          } else {
            throw new Error(`HTTP ${response.status}`);
          }
        } catch (error) {
          console.warn("Failed to fetch stats:", error);
          document.getElementById("stat-connections").textContent = "?";
          document.getElementById("stat-documents").textContent = "?";
          document.getElementById("stat-uptime").textContent = "?";
        }
      }

      // Update stats every 5 seconds
      setInterval(updateStats, 5000);
      updateStats();

      // Add some helpful keyboard shortcuts info
      console.log("🎉 Tiptap Collaborative Editor initialized successfully!");
      console.log("💡 Tip: Try these Markdown shortcuts in the editor:");
      console.log('   - Type "# " for heading 1');
      console.log('   - Type "## " for heading 2');
      console.log('   - Type "- " for bullet list');
      console.log('   - Type "1. " for numbered list');
      console.log('   - Type "> " for blockquote');
      console.log("   - Use **bold** and *italic* formatting");
    </script>
  </body>
</html>
