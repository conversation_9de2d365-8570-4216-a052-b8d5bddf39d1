# Docker Environment Configuration
# This file contains environment variables for Docker deployment
# Requires Node.js 22.7.0+ and Redis 7.4+

# Server Configuration
PORT=3000
HOST=0.0.0.0
NODE_ENV=production

# CORS Configuration
CORS_ORIGIN=*

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined

# YJS Configuration
YJS_PERSISTENCE=false
YJS_GC_ENABLED=true
YJS_CLEANUP_INTERVAL=300000
YJS_MAX_IDLE_TIME=1800000

# WebSocket Configuration
WS_PING_TIMEOUT=60000
WS_PING_INTERVAL=25000
WS_MAX_CONNECTIONS=1000
