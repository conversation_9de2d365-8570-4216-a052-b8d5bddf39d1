# Makefile for Realtime YJS Server Docker Management
# Usage: make [target]

# Variables
IMAGE_NAME := realtime-yjs-server
CONTAINER_NAME := realtime-yjs-server
PORT := 3000
ENV_FILE := .env.docker

# Colors for output
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

# Default target
.DEFAULT_GOAL := help

# Help target
.PHONY: help
help: ## Show this help message
	@echo "$(BLUE)Realtime YJS Server - Docker Management$(NC)"
	@echo ""
	@echo "$(GREEN)Available targets:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(GREEN)Examples:$(NC)"
	@echo "  make build          # Build the Docker image"
	@echo "  make run            # Run the container"
	@echo "  make rebuild        # Rebuild and run"
	@echo "  make logs           # View container logs"
	@echo "  make clean          # Stop and remove container"

# Build targets
.PHONY: build
build: ## Build the Docker image
	@echo "$(BLUE)[BUILD]$(NC) Building Docker image: $(IMAGE_NAME)"
	docker build -t $(IMAGE_NAME) .
	@echo "$(GREEN)[SUCCESS]$(NC) Docker image built successfully"

.PHONY: build-no-cache
build-no-cache: ## Build the Docker image without cache
	@echo "$(BLUE)[BUILD]$(NC) Building Docker image without cache: $(IMAGE_NAME)"
	docker build --no-cache -t $(IMAGE_NAME) .
	@echo "$(GREEN)[SUCCESS]$(NC) Docker image built successfully (no cache)"

# Run targets
.PHONY: run
run: ## Run the Docker container
	@echo "$(BLUE)[RUN]$(NC) Starting container: $(CONTAINER_NAME)"
	@if docker ps -a --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		echo "$(YELLOW)[WARNING]$(NC) Container already exists. Stopping and removing..."; \
		$(MAKE) stop; \
		$(MAKE) remove; \
	fi
	@if [ ! -f "$(ENV_FILE)" ]; then \
		echo "$(YELLOW)[WARNING]$(NC) Environment file $(ENV_FILE) not found. Using default environment."; \
		docker run -d \
			--name $(CONTAINER_NAME) \
			-p $(PORT):3000 \
			-v $$(pwd)/logs:/app/logs \
			--restart unless-stopped \
			$(IMAGE_NAME); \
	else \
		docker run -d \
			--name $(CONTAINER_NAME) \
			-p $(PORT):3000 \
			--env-file $(ENV_FILE) \
			-v $$(pwd)/logs:/app/logs \
			--restart unless-stopped \
			$(IMAGE_NAME); \
	fi
	@echo "$(GREEN)[SUCCESS]$(NC) Container started successfully!"
	@echo "$(BLUE)[INFO]$(NC) Container ID: $$(docker ps -q -f name=$(CONTAINER_NAME))"
	@echo "$(BLUE)[INFO]$(NC) Access the application at: http://localhost:$(PORT)"

.PHONY: run-foreground
run-foreground: ## Run the Docker container in foreground
	@echo "$(BLUE)[RUN]$(NC) Starting container in foreground: $(CONTAINER_NAME)"
	@if docker ps -a --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		echo "$(YELLOW)[WARNING]$(NC) Container already exists. Stopping and removing..."; \
		$(MAKE) stop; \
		$(MAKE) remove; \
	fi
	@if [ ! -f "$(ENV_FILE)" ]; then \
		docker run --rm \
			--name $(CONTAINER_NAME) \
			-p $(PORT):3000 \
			-v $$(pwd)/logs:/app/logs \
			$(IMAGE_NAME); \
	else \
		docker run --rm \
			--name $(CONTAINER_NAME) \
			-p $(PORT):3000 \
			--env-file $(ENV_FILE) \
			-v $$(pwd)/logs:/app/logs \
			$(IMAGE_NAME); \
	fi

# Rebuild targets
.PHONY: rebuild
rebuild: build run ## Rebuild the image and run the container
	@echo "$(GREEN)[SUCCESS]$(NC) Rebuild and run completed"

.PHONY: rebuild-no-cache
rebuild-no-cache: build-no-cache run ## Rebuild the image without cache and run the container
	@echo "$(GREEN)[SUCCESS]$(NC) Rebuild (no cache) and run completed"

# Container management
.PHONY: start
start: ## Start the existing container
	@echo "$(BLUE)[START]$(NC) Starting container: $(CONTAINER_NAME)"
	@if docker ps --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		echo "$(YELLOW)[WARNING]$(NC) Container is already running"; \
	elif docker ps -a --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		docker start $(CONTAINER_NAME); \
		echo "$(GREEN)[SUCCESS]$(NC) Container started"; \
	else \
		echo "$(RED)[ERROR]$(NC) Container doesn't exist. Use 'make run' to create and start it."; \
		exit 1; \
	fi

.PHONY: stop
stop: ## Stop the running container
	@echo "$(BLUE)[STOP]$(NC) Stopping container: $(CONTAINER_NAME)"
	@if docker ps --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		docker stop $(CONTAINER_NAME); \
		echo "$(GREEN)[SUCCESS]$(NC) Container stopped"; \
	else \
		echo "$(YELLOW)[WARNING]$(NC) Container is not running"; \
	fi

.PHONY: restart
restart: ## Restart the container
	@echo "$(BLUE)[RESTART]$(NC) Restarting container: $(CONTAINER_NAME)"
	@if docker ps -a --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		docker restart $(CONTAINER_NAME); \
		echo "$(GREEN)[SUCCESS]$(NC) Container restarted"; \
	else \
		echo "$(RED)[ERROR]$(NC) Container doesn't exist"; \
		exit 1; \
	fi

.PHONY: remove
remove: ## Remove the container
	@echo "$(BLUE)[REMOVE]$(NC) Removing container: $(CONTAINER_NAME)"
	@if docker ps -a --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		docker rm $(CONTAINER_NAME); \
		echo "$(GREEN)[SUCCESS]$(NC) Container removed"; \
	else \
		echo "$(YELLOW)[WARNING]$(NC) Container doesn't exist"; \
	fi

.PHONY: clean
clean: stop remove ## Stop and remove the container
	@echo "$(GREEN)[SUCCESS]$(NC) Container cleaned up"

# Monitoring and debugging
.PHONY: status
status: ## Show container status and stats
	@echo "$(BLUE)[STATUS]$(NC) Container status:"
	@if docker ps -a --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		docker ps -a --filter "name=$(CONTAINER_NAME)" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"; \
		echo ""; \
		if docker ps --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
			echo "$(BLUE)[STATS]$(NC) Container stats:"; \
			docker stats $(CONTAINER_NAME) --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"; \
		fi \
	else \
		echo "$(YELLOW)[WARNING]$(NC) Container doesn't exist"; \
	fi

.PHONY: logs
logs: ## Show container logs
	@echo "$(BLUE)[LOGS]$(NC) Container logs:"
	@if docker ps -a --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		docker logs $(CONTAINER_NAME); \
	else \
		echo "$(RED)[ERROR]$(NC) Container doesn't exist"; \
		exit 1; \
	fi

.PHONY: logs-follow
logs-follow: ## Follow container logs
	@echo "$(BLUE)[LOGS]$(NC) Following container logs (Press Ctrl+C to stop):"
	@if docker ps -a --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		docker logs -f $(CONTAINER_NAME); \
	else \
		echo "$(RED)[ERROR]$(NC) Container doesn't exist"; \
		exit 1; \
	fi

.PHONY: shell
shell: ## Open shell in the running container
	@echo "$(BLUE)[SHELL]$(NC) Opening shell in container: $(CONTAINER_NAME)"
	@if docker ps --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		docker exec -it $(CONTAINER_NAME) /bin/sh; \
	else \
		echo "$(RED)[ERROR]$(NC) Container is not running"; \
		exit 1; \
	fi

.PHONY: health
health: ## Check application health
	@echo "$(BLUE)[HEALTH]$(NC) Checking application health:"
	@if docker ps --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		PORT_MAPPING=$$(docker port $(CONTAINER_NAME) 3000 | cut -d: -f2); \
		if [ -n "$$PORT_MAPPING" ]; then \
			if curl -s "http://localhost:$$PORT_MAPPING/health" > /dev/null; then \
				echo "$(GREEN)[SUCCESS]$(NC) Application is healthy"; \
				curl -s "http://localhost:$$PORT_MAPPING/health" | jq . 2>/dev/null || curl -s "http://localhost:$$PORT_MAPPING/health"; \
			else \
				echo "$(RED)[ERROR]$(NC) Application health check failed"; \
				exit 1; \
			fi \
		else \
			echo "$(RED)[ERROR]$(NC) Could not determine container port"; \
			exit 1; \
		fi \
	else \
		echo "$(RED)[ERROR]$(NC) Container is not running"; \
		exit 1; \
	fi



# Docker Compose targets
.PHONY: compose-up
compose-up: ## Start services using Docker Compose (includes Redis)
	@echo "$(BLUE)[COMPOSE]$(NC) Starting services with Docker Compose"
	docker-compose up -d
	@echo "$(GREEN)[SUCCESS]$(NC) Services started"

.PHONY: compose-down
compose-down: ## Stop services using Docker Compose
	@echo "$(BLUE)[COMPOSE]$(NC) Stopping services with Docker Compose"
	docker-compose down
	@echo "$(GREEN)[SUCCESS]$(NC) Services stopped"

.PHONY: compose-logs
compose-logs: ## Show Docker Compose logs
	docker-compose logs

.PHONY: compose-logs-follow
compose-logs-follow: ## Follow Docker Compose logs
	docker-compose logs -f

.PHONY: redis-start
redis-start: ## Start only Redis service
	@echo "$(BLUE)[REDIS]$(NC) Starting Redis service"
	docker-compose up -d redis
	@echo "$(GREEN)[SUCCESS]$(NC) Redis started"

.PHONY: redis-stop
redis-stop: ## Stop Redis service
	@echo "$(BLUE)[REDIS]$(NC) Stopping Redis service"
	docker-compose stop redis
	@echo "$(GREEN)[SUCCESS]$(NC) Redis stopped"

# Cleanup targets
.PHONY: clean-all
clean-all: clean ## Clean up everything (container and image)
	@echo "$(BLUE)[CLEAN]$(NC) Removing Docker image: $(IMAGE_NAME)"
	@if docker images --format 'table {{.Repository}}' | grep -q "^$(IMAGE_NAME)$$"; then \
		docker rmi $(IMAGE_NAME); \
		echo "$(GREEN)[SUCCESS]$(NC) Docker image removed"; \
	else \
		echo "$(YELLOW)[WARNING]$(NC) Docker image doesn't exist"; \
	fi

.PHONY: prune
prune: ## Remove unused Docker resources
	@echo "$(BLUE)[PRUNE]$(NC) Removing unused Docker resources"
	docker system prune -f
	@echo "$(GREEN)[SUCCESS]$(NC) Unused Docker resources removed"

# Development targets
.PHONY: dev
dev: ## Run in development mode (foreground with logs)
	@echo "$(BLUE)[DEV]$(NC) Starting in development mode"
	$(MAKE) run-foreground

.PHONY: test
test: ## Test the Docker setup
	@echo "$(BLUE)[TEST]$(NC) Testing Docker setup"
	$(MAKE) build
	$(MAKE) run
	@sleep 5
	$(MAKE) health
	$(MAKE) clean
	@echo "$(GREEN)[SUCCESS]$(NC) Docker setup test completed"
