# Makefile for Realtime YJS Server Docker Management
# Usage: make [target]

# Variables
IMAGE_NAME := realtime-yjs-server
CONTAINER_NAME := realtime-yjs-server
PORT := 3000

# Colors for output
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

# Default target
.DEFAULT_GOAL := help

# Help target
.PHONY: help
help: ## Show this help message
	@echo "$(BLUE)Realtime YJS Server - Docker Management$(NC)"
	@echo ""
	@echo "$(GREEN)Available commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Build the Docker image
.PHONY: build
build: ## Build the Docker image
	@echo "$(BLUE)[BUILD]$(NC) Building Docker image: $(IMAGE_NAME)"
	docker build -t $(IMAGE_NAME) .
	@echo "$(GREEN)[SUCCESS]$(NC) Docker image built successfully"

# Run the Docker container with real-time logs
.PHONY: run
run: ## Run the Docker container with real-time logs
	@echo "$(BLUE)[RUN]$(NC) Starting container: $(CONTAINER_NAME)"
	@if docker ps -a --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		echo "$(YELLOW)[WARNING]$(NC) Container already exists. Stopping and removing..."; \
		$(MAKE) stop; \
		docker rm $(CONTAINER_NAME) 2>/dev/null || true; \
	fi
	@echo "$(GREEN)[INFO]$(NC) Starting container with real-time logs (Press Ctrl+C to stop)"
	@echo "$(BLUE)[INFO]$(NC) Access the application at: http://localhost:$(PORT)"
	@echo "$(YELLOW)[LOGS]$(NC) Container logs:"
	@if [ ! -f ".env" ]; then \
		echo "$(YELLOW)[WARNING]$(NC) .env file not found. Using default environment."; \
		docker run --rm \
			--name $(CONTAINER_NAME) \
			-p $(PORT):3000 \
			-v $$(pwd)/logs:/app/logs \
			$(IMAGE_NAME); \
	else \
		docker run --rm \
			--name $(CONTAINER_NAME) \
			-p $(PORT):3000 \
			--env-file .env \
			-v $$(pwd)/logs:/app/logs \
			$(IMAGE_NAME); \
	fi

# Run the Docker container in background (detached)
.PHONY: run-detached
run-detached: ## Run the Docker container in background
	@echo "$(BLUE)[RUN]$(NC) Starting container in background: $(CONTAINER_NAME)"
	@if docker ps -a --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		echo "$(YELLOW)[WARNING]$(NC) Container already exists. Stopping and removing..."; \
		$(MAKE) stop; \
		docker rm $(CONTAINER_NAME) 2>/dev/null || true; \
	fi
	@if [ ! -f ".env" ]; then \
		echo "$(YELLOW)[WARNING]$(NC) .env file not found. Using default environment."; \
		docker run -d \
			--name $(CONTAINER_NAME) \
			-p $(PORT):3000 \
			-v $$(pwd)/logs:/app/logs \
			--restart unless-stopped \
			$(IMAGE_NAME); \
	else \
		docker run -d \
			--name $(CONTAINER_NAME) \
			-p $(PORT):3000 \
			--env-file .env \
			-v $$(pwd)/logs:/app/logs \
			--restart unless-stopped \
			$(IMAGE_NAME); \
	fi
	@echo "$(GREEN)[SUCCESS]$(NC) Container started in background!"
	@echo "$(BLUE)[INFO]$(NC) Access the application at: http://localhost:$(PORT)"
	@echo "$(BLUE)[INFO]$(NC) Use 'make logs' to view logs or 'make shell' to access container"

# Clean up Docker cache and unused resources
.PHONY: clean
clean: ## Clean Docker cache and unused resources
	@echo "$(BLUE)[CLEAN]$(NC) Cleaning Docker cache and unused resources"
	docker system prune -f
	docker builder prune -f
	@echo "$(GREEN)[SUCCESS]$(NC) Docker cache cleaned"

# Nuclear option - remove everything related to this project
.PHONY: armageddon
armageddon: ## Remove ALL project-related Docker resources (container, image, volumes)
	@echo "$(RED)[ARMAGEDDON]$(NC) This will remove ALL resources for $(IMAGE_NAME)!"
	@echo "$(YELLOW)[WARNING]$(NC) Press Ctrl+C within 5 seconds to cancel..."
	@sleep 5
	@echo "$(RED)[ARMAGEDDON]$(NC) Stopping and removing project container..."
	-docker stop $(CONTAINER_NAME) 2>/dev/null
	-docker rm $(CONTAINER_NAME) 2>/dev/null
	@echo "$(RED)[ARMAGEDDON]$(NC) Removing project image..."
	-docker rmi $(IMAGE_NAME) 2>/dev/null
	@echo "$(RED)[ARMAGEDDON]$(NC) Removing project volumes..."
	-docker volume rm $$(docker volume ls -q --filter "name=$(CONTAINER_NAME)") 2>/dev/null
	@echo "$(RED)[ARMAGEDDON]$(NC) Removing dangling images and build cache for this project..."
	-docker image prune -f 2>/dev/null
	-docker builder prune -f 2>/dev/null
	@echo "$(GREEN)[SUCCESS]$(NC) Project armageddon completed - all $(IMAGE_NAME) resources removed!"

# Open shell in the running container
.PHONY: shell
shell: ## Open shell in the running container
	@echo "$(BLUE)[SHELL]$(NC) Opening shell in container: $(CONTAINER_NAME)"
	@if docker ps --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		docker exec -it $(CONTAINER_NAME) /bin/sh; \
	else \
		echo "$(RED)[ERROR]$(NC) Container is not running. Use 'make run' first."; \
		exit 1; \
	fi

# Check application health
.PHONY: health
health: ## Check application health
	@echo "$(BLUE)[HEALTH]$(NC) Checking application health:"
	@if docker ps --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		PORT_MAPPING=$$(docker port $(CONTAINER_NAME) 3000 | cut -d: -f2); \
		if [ -n "$$PORT_MAPPING" ]; then \
			if curl -s "http://localhost:$$PORT_MAPPING/health" > /dev/null; then \
				echo "$(GREEN)[SUCCESS]$(NC) Application is healthy"; \
				curl -s "http://localhost:$$PORT_MAPPING/health" | jq . 2>/dev/null || curl -s "http://localhost:$$PORT_MAPPING/health"; \
			else \
				echo "$(RED)[ERROR]$(NC) Application health check failed"; \
				exit 1; \
			fi \
		else \
			echo "$(RED)[ERROR]$(NC) Could not determine container port"; \
			exit 1; \
		fi \
	else \
		echo "$(RED)[ERROR]$(NC) Container is not running. Use 'make run' first."; \
		exit 1; \
	fi

# View container logs
.PHONY: logs
logs: ## View container logs (real-time)
	@echo "$(BLUE)[LOGS]$(NC) Following container logs (Press Ctrl+C to stop):"
	@if docker ps --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		docker logs -f $(CONTAINER_NAME); \
	else \
		echo "$(RED)[ERROR]$(NC) Container is not running. Use 'make run' or 'make run-detached' first."; \
		exit 1; \
	fi

# Stop the running container
.PHONY: stop
stop: ## Stop the running container
	@echo "$(BLUE)[STOP]$(NC) Stopping container: $(CONTAINER_NAME)"
	@if docker ps --format 'table {{.Names}}' | grep -q "^$(CONTAINER_NAME)$$"; then \
		docker stop $(CONTAINER_NAME); \
		echo "$(GREEN)[SUCCESS]$(NC) Container stopped"; \
	else \
		echo "$(YELLOW)[WARNING]$(NC) Container is not running"; \
	fi
