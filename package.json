{"name": "tiptap-collaborative-server", "version": "1.0.0", "description": "A real-time collaborative rich text editor server using Tiptap, YJS and y-websocket with SOLID principles", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "build:bundle": "esbuild src/tiptap-bundle-source.js --bundle --outfile=tiptap-bundle.js --format=iife --global-name=TiptapBundle --minify", "build:bundle:dev": "esbuild src/tiptap-bundle-source.js --bundle --outfile=tiptap-bundle.js --format=iife --global-name=TiptapBundle", "docker:build": "docker build -t realtime-yjs-server .", "docker:run": "docker run -p 3000:3000 realtime-yjs-server"}, "keywords": ["tiptap", "rich-text-editor", "yjs", "y-websocket", "realtime", "collaboration", "websocket", "nodejs", "prose<PERSON><PERSON>r"], "author": "", "license": "MIT", "dependencies": {"@tiptap/core": "^2.24.2", "@tiptap/extension-collaboration": "^2.24.2", "@tiptap/extension-collaboration-cursor": "^2.24.2", "@tiptap/extension-color": "^2.24.2", "@tiptap/extension-font-family": "^2.24.2", "@tiptap/extension-highlight": "^2.24.2", "@tiptap/extension-link": "^2.24.2", "@tiptap/extension-subscript": "^2.24.2", "@tiptap/extension-superscript": "^2.24.2", "@tiptap/extension-table": "^2.24.2", "@tiptap/extension-table-cell": "^2.24.2", "@tiptap/extension-table-header": "^2.24.2", "@tiptap/extension-table-row": "^2.24.2", "@tiptap/extension-text-align": "^2.24.2", "@tiptap/extension-text-style": "^2.24.2", "@tiptap/extension-underline": "^2.24.2", "@tiptap/pm": "^2.24.2", "@tiptap/starter-kit": "^2.24.2", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lib0": "^0.2.109", "redis": "^5.5.6", "winston": "^3.11.0", "ws": "^8.18.3", "y-websocket": "^1.5.0", "yjs": "^13.6.27"}, "devDependencies": {"esbuild": "^0.25.5", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}