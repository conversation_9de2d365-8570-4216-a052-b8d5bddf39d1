# Environment Configuration for WebSocket Collaboration Server with Authentication
# Requires Node.js 22.7.0+ and Redis 7.4+

# Server Configuration
PORT=3000
HOST=0.0.0.0
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-must-be-same-as-django
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
JWT_ISSUER=collaboration-server
JWT_AUDIENCE=collaboration-clients

# Removed Django Integration - Now using JWT-only authentication

# Redis Configuration (Redis 7.4+ recommended)
REDIS_URL=redis://localhost:6379
REDIS_KEY_PREFIX=collab:
REDIS_USER_TTL=900
REDIS_ACCESS_TTL=300
REDIS_BLACKLIST_TTL=86400

# Security Settings
MAX_CONNECTIONS_PER_USER=5
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000
CORS_ENABLED=true

# CORS Configuration
CORS_ORIGIN=*

# Document Settings
MAX_DOCUMENT_SIZE=10485760
AUTO_SAVE_INTERVAL=30000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined
ENABLE_AUTH_LOGS=true
ENABLE_ACCESS_LOGS=true
LOG_FAILED_ATTEMPTS=true

# YJS Configuration
YJS_PERSISTENCE=false
YJS_GC_ENABLED=true

# WebSocket Configuration
WS_PING_TIMEOUT=60000
WS_PING_INTERVAL=25000

# Document Access Control
# Set to false to deny access by default (requires explicit permissions in JWT)
DEFAULT_DOCUMENT_ACCESS=true

# Feature Flags
ENABLE_TOKEN_REFRESH=true
ENABLE_USER_PRESENCE=true
ENABLE_DOCUMENT_LOCKING=false
ENABLE_AUDIT_LOGGING=true
ENABLE_RATE_LIMITING=true

# Production Settings (uncomment for production)
# NODE_ENV=production
# LOG_LEVEL=warn
# ENABLE_AUTH_LOGS=false
# ENABLE_ACCESS_LOGS=false
